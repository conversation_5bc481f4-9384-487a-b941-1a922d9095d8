/**
 * Comprehensive Onboarding System
 *
 * Handles first-time setup, provider configuration, API key management,
 * and seamless transition to interactive mode.
 */
import { existsSync, appendFileSync } from 'fs';
import { homedir } from 'os';
import { join } from 'path';
import chalk from 'chalk';
import { loadConfig, saveConfig, getApiKey, clearConfigCache } from './config.js';
import { getProviderNames, getProvider } from './providers.js';
import { validateApiKey } from './get-api-key.js';
import { logInfo, logError, logWarn } from './logger/log.js';
/**
 * Check if onboarding is needed
 */
export function needsOnboarding(config) {
    const currentConfig = config || loadConfig();
    // Check if we have a valid provider
    if (!currentConfig.provider || !getProvider(currentConfig.provider)) {
        return true;
    }
    // Check if we have an API key for the provider
    const apiKey = getApiKey(currentConfig.provider);
    if (!apiKey && currentConfig.provider !== 'ollama') {
        return true;
    }
    // Check if configuration file exists
    const configPath = getConfigPath();
    if (!existsSync(configPath)) {
        return true;
    }
    return false;
}
/**
 * Check if onboarding is needed with API key validation
 */
export async function needsOnboardingWithValidation(config) {
    const currentConfig = config || loadConfig();
    // Check if we have a valid provider
    if (!currentConfig.provider || !getProvider(currentConfig.provider)) {
        return true;
    }
    // Check if we have an API key for the provider
    const apiKey = getApiKey(currentConfig.provider);
    if (!apiKey && currentConfig.provider !== 'ollama') {
        return true;
    }
    // Check if configuration file exists
    const configPath = getConfigPath();
    if (!existsSync(configPath)) {
        return true;
    }
    // For non-Ollama providers, validate the API key
    if (currentConfig.provider !== 'ollama' && apiKey) {
        try {
            const isValid = await validateApiKey(currentConfig.provider, apiKey);
            if (!isValid) {
                return true; // Invalid API key, need onboarding
            }
        }
        catch (_error) {
            return true; // Validation failed, need onboarding
        }
    }
    return false;
}
/**
 * Get configuration file path
 */
function getConfigPath() {
    return join(homedir(), '.kritrima-ai', 'config.json');
}
/**
 * Display welcome message
 */
export function displayWelcomeMessage() {
    console.clear();
    console.log(chalk.cyan('╔══════════════════════════════════════════════════════════════╗'));
    console.log(chalk.cyan('║                 Welcome to Kritrima AI CLI!                 ║'));
    console.log(chalk.cyan('║              Autonomous Coding Assistant                    ║'));
    console.log(chalk.cyan('╚══════════════════════════════════════════════════════════════╝'));
    console.log('');
    console.log(chalk.green('🚀 Let\'s get you set up with your AI coding assistant!'));
    console.log('');
    console.log(chalk.blue('This setup wizard will help you:'));
    console.log(chalk.blue('  • Choose an AI provider'));
    console.log(chalk.blue('  • Configure your API key'));
    console.log(chalk.blue('  • Set up your preferences'));
    console.log(chalk.blue('  • Test your configuration'));
    console.log('');
}
/**
 * Display provider selection menu
 */
export function displayProviderMenu() {
    console.log(chalk.cyan('📋 Available AI Providers:'));
    console.log('');
    const providers = getProviderNames();
    providers.forEach((providerName, index) => {
        const provider = getProvider(providerName);
        if (provider) {
            const number = chalk.yellow(`${index + 1}.`);
            const name = chalk.green(provider.name);
            const models = provider.models?.slice(0, 3).join(', ') || 'Various models';
            console.log(`${number} ${name}`);
            console.log(`   ${chalk.gray(models)}`);
            console.log('');
        }
    });
}
/**
 * Get provider choice from user
 */
export async function getProviderChoice() {
    const { default: inquirer } = await import('inquirer');
    const providers = getProviderNames();
    const choices = providers.map(providerName => {
        const provider = getProvider(providerName);
        return {
            name: `${provider?.name || providerName} - ${provider?.models?.[0] || 'Various models'}`,
            value: providerName,
            short: provider?.name || providerName,
        };
    });
    const { provider } = await inquirer.prompt([
        {
            type: 'list',
            name: 'provider',
            message: 'Choose your AI provider:',
            choices,
            pageSize: 10,
        },
    ]);
    return provider;
}
/**
 * Get API key from user
 */
export async function getApiKeyFromUser(provider) {
    const { default: inquirer } = await import('inquirer');
    const providerConfig = getProvider(provider);
    if (!providerConfig) {
        throw new Error(`Unknown provider: ${provider}`);
    }
    console.log('');
    console.log(chalk.cyan(`🔑 API Key Setup for ${providerConfig.name}`));
    console.log('');
    // Show instructions for getting API key
    displayApiKeyInstructions(provider, providerConfig);
    const { apiKey } = await inquirer.prompt([
        {
            type: 'password',
            name: 'apiKey',
            message: `Enter your ${providerConfig.name} API key:`,
            mask: '*',
            validate: (input) => {
                if (!input || input.trim().length === 0) {
                    return 'API key is required';
                }
                if (input.length < 10) {
                    return 'API key seems too short';
                }
                return true;
            },
        },
    ]);
    return apiKey.trim();
}
/**
 * Display API key instructions for provider
 */
function displayApiKeyInstructions(provider, config) {
    const instructions = {
        openai: [
            '1. Go to https://platform.openai.com/account/api-keys',
            '2. Click "Create new secret key"',
            '3. Copy the generated key (starts with sk-)',
        ],
        azure: [
            '1. Go to your Azure OpenAI resource in Azure Portal',
            '2. Navigate to "Keys and Endpoint"',
            '3. Copy one of the API keys',
        ],
        gemini: [
            '1. Go to https://makersuite.google.com/app/apikey',
            '2. Click "Create API key"',
            '3. Copy the generated key',
        ],
        mistral: [
            '1. Go to https://console.mistral.ai/api-keys/',
            '2. Click "Create new key"',
            '3. Copy the generated key',
        ],
        deepseek: [
            '1. Go to https://platform.deepseek.com/api_keys',
            '2. Click "Create API Key"',
            '3. Copy the generated key',
        ],
        xai: [
            '1. Go to https://console.x.ai/api-keys',
            '2. Click "Create API Key"',
            '3. Copy the generated key',
        ],
        groq: [
            '1. Go to https://console.groq.com/keys',
            '2. Click "Create API Key"',
            '3. Copy the generated key',
        ],
        ollama: [
            '1. Install Ollama from https://ollama.ai',
            '2. Run: ollama serve',
            '3. No API key needed for local Ollama',
        ],
    };
    const providerInstructions = instructions[provider.toLowerCase()];
    if (providerInstructions) {
        console.log(chalk.blue(`To get your ${config.name} API key:`));
        providerInstructions.forEach(step => {
            console.log(chalk.gray(`  ${step}`));
        });
        console.log('');
    }
}
/**
 * Validate API key with provider
 */
export async function validateApiKeyWithProvider(provider, apiKey) {
    console.log(chalk.blue('🔍 Validating API key...'));
    try {
        // Set environment variable temporarily for validation
        const providerConfig = getProvider(provider);
        if (!providerConfig) {
            console.log(chalk.red(`❌ Unknown provider: ${provider}`));
            return false;
        }
        const envKey = providerConfig.envKey;
        const originalValue = process.env[envKey];
        process.env[envKey] = apiKey;
        logInfo(`Validating API key for ${provider} using model ${providerConfig.defaultModel}`);
        const isValid = await validateApiKey(provider, apiKey);
        // Restore original value
        if (originalValue !== undefined) {
            process.env[envKey] = originalValue;
        }
        else {
            delete process.env[envKey];
        }
        if (isValid) {
            console.log(chalk.green('✅ API key is valid!'));
            return true;
        }
        else {
            console.log(chalk.red('❌ API key validation failed'));
            console.log(chalk.yellow('Please check:'));
            console.log(chalk.yellow('  • API key is correct and not expired'));
            console.log(chalk.yellow('  • API key has the necessary permissions'));
            console.log(chalk.yellow('  • Your account has sufficient credits/quota'));
            return false;
        }
    }
    catch (error) {
        console.log(chalk.red('❌ API key validation failed'));
        console.log(chalk.yellow('Error details:'), error.message);
        logError('API key validation error', error);
        return false;
    }
}
/**
 * Save API key to environment
 */
export function saveApiKeyToEnvironment(provider, apiKey) {
    const providerConfig = getProvider(provider);
    if (!providerConfig) {
        throw new Error(`Unknown provider: ${provider}`);
    }
    const envKey = providerConfig.envKey;
    process.env[envKey] = apiKey;
    // Also save to shell profile for persistence
    saveApiKeyToProfile(envKey, apiKey);
}
/**
 * Save API key to shell profile
 */
function saveApiKeyToProfile(envKey, apiKey) {
    const profiles = [
        '.bashrc',
        '.zshrc',
        '.profile',
    ];
    const exportLine = `export ${envKey}="${apiKey}"\n`;
    for (const profile of profiles) {
        const profilePath = join(homedir(), profile);
        if (existsSync(profilePath)) {
            try {
                appendFileSync(profilePath, `\n# Kritrima AI CLI\n${exportLine}`);
                console.log(chalk.green(`✅ API key saved to ${profile}`));
                break;
            }
            catch (error) {
                logWarn(`Failed to save to ${profile}`, error);
            }
        }
    }
}
/**
 * Get model choice from user
 */
export async function getModelChoice(provider) {
    const { default: inquirer } = await import('inquirer');
    const providerConfig = getProvider(provider);
    if (!providerConfig || !providerConfig.models) {
        return providerConfig?.defaultModel || 'gpt-4';
    }
    const choices = providerConfig.models.map(model => ({
        name: model,
        value: model,
    }));
    const { model } = await inquirer.prompt([
        {
            type: 'list',
            name: 'model',
            message: `Choose a model for ${providerConfig.name}:`,
            choices,
            default: providerConfig.defaultModel,
            pageSize: 10,
        },
    ]);
    return model;
}
/**
 * Get additional preferences from user
 */
export async function getAdditionalPreferences() {
    const { default: inquirer } = await import('inquirer');
    const preferences = await inquirer.prompt([
        {
            type: 'list',
            name: 'approvalMode',
            message: 'Choose approval mode:',
            choices: [
                {
                    name: 'Suggest - Manual approval for all actions (Recommended)',
                    value: 'suggest',
                },
                {
                    name: 'Auto-edit - Automatic file edits, manual command approval',
                    value: 'auto-edit',
                },
                {
                    name: 'Full-auto - Automatic everything (Use with caution)',
                    value: 'full-auto',
                },
            ],
            default: 'suggest',
        },
        {
            type: 'confirm',
            name: 'notifications',
            message: 'Enable desktop notifications?',
            default: true,
        },
        {
            type: 'confirm',
            name: 'saveHistory',
            message: 'Save command history?',
            default: true,
        },
        {
            type: 'confirm',
            name: 'debug',
            message: 'Enable debug logging?',
            default: false,
        },
    ]);
    return preferences;
}
/**
 * Run complete onboarding process
 */
export async function runOnboarding(options = {}) {
    try {
        logInfo('Starting onboarding process');
        // Check if we should skip onboarding (unless forced)
        if (!options.force && !needsOnboarding()) {
            console.log(chalk.blue('ℹ️  Configuration already exists. Use --force to override.'));
            return {
                success: true,
                config: loadConfig(),
                needsSetup: false,
            };
        }
        // Display welcome message
        displayWelcomeMessage();
        // Get provider choice
        let provider = options.provider;
        if (!provider) {
            provider = await getProviderChoice();
        }
        logInfo(`Selected provider: ${provider}`);
        // Get API key (skip for Ollama)
        let apiKey = '';
        if (provider !== 'ollama') {
            apiKey = await getApiKeyFromUser(provider);
            // Validate API key unless skipped
            if (!options.skipValidation) {
                const isValid = await validateApiKeyWithProvider(provider, apiKey);
                if (!isValid) {
                    const { default: inquirer } = await import('inquirer');
                    const { retry } = await inquirer.prompt([
                        {
                            type: 'confirm',
                            name: 'retry',
                            message: 'API key validation failed. Would you like to try again?',
                            default: true,
                        },
                    ]);
                    if (retry) {
                        return runOnboarding(options);
                    }
                    else {
                        return {
                            success: false,
                            config: loadConfig(),
                            needsSetup: true,
                            error: 'API key validation failed',
                        };
                    }
                }
            }
            // Save API key to environment
            saveApiKeyToEnvironment(provider, apiKey);
        }
        // Get model choice
        const model = await getModelChoice(provider);
        logInfo(`Selected model: ${model}`);
        // Get additional preferences
        const preferences = await getAdditionalPreferences();
        // Create configuration
        const config = {
            provider,
            model,
            approvalMode: preferences.approvalMode || 'suggest',
            maxTokens: 4096,
            temperature: 0.7,
            timeout: 30000,
            debug: preferences.debug || false,
            notifications: preferences.notifications !== false,
            saveHistory: preferences.saveHistory !== false,
            maxHistorySize: 1000,
            maxIterations: 10,
            workingDirectory: process.cwd(),
            additionalWritableRoots: [],
            disableResponseStorage: false,
        };
        // Save configuration
        saveConfig(config, true); // Save globally
        clearConfigCache(); // Clear cache to reload
        console.log('');
        console.log(chalk.green('🎉 Setup completed successfully!'));
        console.log('');
        console.log(chalk.blue('Your configuration:'));
        console.log(chalk.blue(`  Provider: ${config.provider}`));
        console.log(chalk.blue(`  Model: ${config.model}`));
        console.log(chalk.blue(`  Approval Mode: ${config.approvalMode}`));
        console.log('');
        console.log(chalk.green('Starting interactive mode...'));
        console.log('');
        return {
            success: true,
            config,
            needsSetup: false,
        };
    }
    catch (error) {
        logError('Onboarding failed', error);
        return {
            success: false,
            config: loadConfig(),
            needsSetup: true,
            error: error.message,
        };
    }
}
/**
 * Quick setup for specific provider
 */
export async function quickSetup(provider, apiKey) {
    try {
        logInfo(`Quick setup for provider: ${provider}`);
        // Validate provider
        const providerConfig = getProvider(provider);
        if (!providerConfig) {
            throw new Error(`Unknown provider: ${provider}`);
        }
        // Get API key if not provided
        if (!apiKey && provider !== 'ollama') {
            apiKey = await getApiKeyFromUser(provider);
        }
        // Validate API key
        if (apiKey && provider !== 'ollama') {
            const isValid = await validateApiKeyWithProvider(provider, apiKey);
            if (!isValid) {
                throw new Error('API key validation failed');
            }
            saveApiKeyToEnvironment(provider, apiKey);
        }
        // Create minimal configuration
        const config = {
            provider,
            model: providerConfig.defaultModel || providerConfig.models?.[0] || 'gpt-4',
            approvalMode: 'suggest',
            maxTokens: 4096,
            temperature: 0.7,
            timeout: 30000,
            debug: false,
            notifications: true,
            saveHistory: true,
            maxHistorySize: 1000,
            maxIterations: 10,
            workingDirectory: process.cwd(),
            additionalWritableRoots: [],
            disableResponseStorage: false,
        };
        // Save configuration
        saveConfig(config, true);
        clearConfigCache();
        console.log(chalk.green('✅ Quick setup completed!'));
        return {
            success: true,
            config,
            needsSetup: false,
        };
    }
    catch (error) {
        logError('Quick setup failed', error);
        return {
            success: false,
            config: loadConfig(),
            needsSetup: true,
            error: error.message,
        };
    }
}
//# sourceMappingURL=onboarding.js.map