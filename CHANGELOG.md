# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.2.4] - 2025-01-30
### Fixed
- **CRITICAL**: Fixed interactive mode not launching when running `kritrima-ai` without arguments
- Improved CLI argument handling using Commander.js action handlers
- Interactive mode now properly launches when no command or arguments are provided

### Changed
- Refactored CLI logic to use proper Commander.js action pattern
- Enhanced user experience for launching interactive mode

## [1.2.3] - 2025-01-30
### Added
- Added `kritrima` as an additional binary command (alongside `kritrima-ai`)
- Comprehensive installation troubleshooting guides

### Fixed
- Improved package installation clarity to prevent old package confusion
- Enhanced documentation for installation issues

### Changed
- Both `kritrima` and `kritrima-ai` commands now available for convenience

## [1.2.2] - 2025-01-30
### Fixed
- **CRITICAL**: Enhanced Sharp dependency handling with more robust error catching
- Improved dynamic import error handling to prevent any module loading issues
- Added silent fallback for Sharp loading failures to ensure CLI never crashes
- Enhanced cross-platform compatibility for image processing

### Changed
- Sharp loading now completely silent on failure (no console warnings)
- More robust error handling in image processing pipeline

## [1.2.1] - 2025-01-30
### Fixed
- **CRITICAL**: Resolved CLI crash on Linux/WSL when Sharp image processing library fails to load native binaries
- Fixed unused variable linting error in input-utils.ts
- Enhanced error handling for optional dependencies

### Changed
- Sharp dependency moved to optionalDependencies for improved cross-platform compatibility
- Added graceful fallback when Sharp is unavailable (image optimization disabled with warning)
- Improved cross-platform reliability and stability

## [1.2.0]
### Changed
- **BREAKING**: Package name changed from `kritrima-ai-cli` to `kritrima` for simpler installation
- Updated all documentation and references to use new package name
- Repository URLs updated to reflect new naming convention
- Sharp image processing library moved to optional dependencies for better cross-platform compatibility

### Added
- Complete project build and cross-platform publishing support
- Comprehensive test suite with Vitest for better reliability
- Enhanced build scripts for Windows 11 WSL, Linux, and macOS
- Improved package.json configuration for npm publishing
- Better error handling and type safety across the codebase
- Graceful fallback when Sharp image processing library is not available

### Fixed
- **CRITICAL**: Fixed crash on Linux/WSL when Sharp native binaries are not available
- Build process optimization and reliability
- Cross-platform compatibility improvements
- Test coverage and reliability enhancements
- Package publishing workflow improvements
- Image processing now works without Sharp (with warning message)

### Technical Improvements
- Added comprehensive test files for CLI functionality
- Enhanced npm scripts for better development workflow
- Improved TypeScript configuration and type checking
- Better cross-platform build and publish process

## [1.1.0] - 2024-12-19

### Added
- Complete cross-platform build and publishing setup
- Automated ES module import fixing for Node.js compatibility
- Comprehensive npm package configuration
- Cross-platform installation support (Windows 11, WSL, Linux, macOS)
- Enhanced build scripts with proper cleanup and asset copying
- Improved error handling and Windows path compatibility

### Fixed
- ES module import issues on Windows systems
- Cross-platform file path handling in bin scripts
- Build process optimization for npm publishing

### Technical Improvements
- Added automated import fixing script for ES modules
- Enhanced package.json with proper npm metadata
- Improved TypeScript compilation settings
- Better cross-platform script compatibility

## [1.0.0] - 2024-12-19

### Added
- Initial release of Kritrima AI CLI
- Multi-provider AI support (OpenAI, Anthropic, and more)
- Autonomous agent capabilities with intelligent code analysis
- Cross-platform compatibility (Windows 11, WSL, Linux, macOS)
- Interactive CLI interface with rich terminal UI
- Flexible approval modes: suggest, auto-edit, full-auto
- Git integration with smart repository awareness
- Comprehensive configuration management
- Provider and model management commands
- Diagnostic tools for troubleshooting
- Real-time feedback and notifications
- Command history saving
- Full-context and single-pass modes
- TypeScript support with React components
- Comprehensive test suite with Vitest
- ESLint configuration for code quality
- Cross-platform build scripts

### Features
- **CLI Commands**:
  - Interactive mode for conversational AI assistance
  - Direct prompt execution
  - Configuration management (`config` command)
  - Provider listing (`providers` command)
  - Model listing (`models` command)
  - System diagnostics (`doctor` command)
  - Update checking (`update` command)

- **Configuration Options**:
  - Provider selection (OpenAI, Anthropic, etc.)
  - Model selection per provider
  - Temperature control for AI responses
  - Token limits and timeout settings
  - Debug logging
  - Desktop notifications
  - Command history management

- **Cross-Platform Support**:
  - Windows 11 native support
  - Windows Subsystem for Linux (WSL) compatibility
  - Linux distribution support
  - macOS compatibility
  - Platform-specific installation instructions

- **Developer Experience**:
  - TypeScript with strict type checking
  - React components for rich UI
  - Comprehensive test coverage
  - ESLint for code quality
  - Hot reload in development mode
  - Detailed error handling and logging

### Technical Details
- **Node.js**: Requires version 22.0.0 or higher
- **Package Type**: ES Modules (ESM)
- **Build System**: TypeScript compiler with custom build scripts
- **Testing**: Vitest with coverage reporting
- **Linting**: ESLint with TypeScript support
- **Dependencies**: Carefully selected for minimal footprint and maximum compatibility

### Documentation
- Comprehensive README with installation and usage instructions
- Platform-specific setup guides
- Configuration reference
- API documentation
- Contributing guidelines
- Troubleshooting guide

### Security
- Secure API key handling
- Environment variable support
- No sensitive data logging
- Safe file operations with proper permissions

---

## Future Releases

### Planned Features
- Plugin system for extensibility
- Custom AI provider support
- Advanced code analysis features
- Integration with popular IDEs
- Team collaboration features
- Cloud synchronization
- Performance optimizations
- Additional language support

### Breaking Changes
None planned for v1.x series. All changes will maintain backward compatibility.

---

For more information about upcoming features and development roadmap, please visit our [GitHub repository](https://github.com/kritrima/kritrima).
