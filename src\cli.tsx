#!/usr/bin/env node

/**
 * Kritrima AI CLI - Main Entry Point
 * 
 * Handles command line argument parsing, configuration loading,
 * and application initialization.
 */

import { Command } from 'commander';
import { existsSync } from 'fs';
import { resolve } from 'path';
import chalk from 'chalk';
import { loadConfig, saveConfig, validateConfig } from './utils/config';
import { getProviderNames, hasProvider, getProvider } from './utils/providers';
import { CLI_VERSION, formatVersionInfo } from './version';
import { logInfo, logError, isLoggingEnabled } from './utils/logger/log';
import { checkForUpdates } from './utils/check-updates';
import { runApp } from './app';
import { needsOnboardingWithValidation, runOnboarding } from './utils/onboarding';
import { runConfigurationWizard } from './utils/config-wizard';
import type { ApprovalPolicy } from './types/index';

/**
 * Main CLI application
 */
async function main(): Promise<void> {
  const program = new Command();

  program
    .name('kritrima-ai')
    .description('Comprehensive AI coding assistant with autonomous agent capabilities')
    .version(CLI_VERSION)
    .option('-m, --model <model>', 'AI model to use')
    .option('-p, --provider <provider>', 'AI provider to use')
    .option('-a, --approval <mode>', 'Approval mode: suggest, auto-edit, full-auto')
    .option('-t, --temperature <temp>', 'Temperature for AI responses (0-2)', parseFloat)
    .option('--max-tokens <tokens>', 'Maximum tokens for responses', parseInt)
    .option('--timeout <ms>', 'Request timeout in milliseconds', parseInt)
    .option('-d, --debug', 'Enable debug logging')
    .option('--no-notifications', 'Disable desktop notifications')
    .option('--no-history', 'Disable command history saving')
    .option('-w, --workdir <path>', 'Working directory')
    .option('--config <path>', 'Custom configuration file path')
    .option('--full-context', 'Enable experimental full-context mode')
    .option('--single-pass', 'Run in single-pass mode with prompt')
    .argument('[prompt]', 'Initial prompt for the AI assistant')
    .action(async (prompt, options) => {
      // This action will be called when no subcommand is used
      await handleMainCommand(prompt, options);
    });

  // Add subcommands
  program
    .command('setup')
    .description('Run interactive setup wizard')
    .option('-p, --provider <provider>', 'Pre-select provider')
    .option('-g, --global', 'Save configuration globally')
    .option('--skip-validation', 'Skip API key validation')
    .option('-f, --force', 'Force setup even if configuration exists')
    .action(handleSetupCommand);

  program
    .command('config')
    .description('Manage configuration')
    .option('-g, --global', 'Use global configuration')
    .option('-l, --list', 'List current configuration')
    .option('-s, --set <key=value>', 'Set configuration value')
    .option('-r, --reset', 'Reset to default configuration')
    .option('-w, --wizard', 'Run configuration wizard')
    .option('-c, --clear', 'Clear all configuration and API keys')
    .action(handleConfigCommand);

  program
    .command('providers')
    .description('List available AI providers')
    .option('-v, --verbose', 'Show detailed provider information')
    .action(handleProvidersCommand);

  program
    .command('models')
    .description('List available models for a provider')
    .argument('[provider]', 'Provider name')
    .action(handleModelsCommand);

  program
    .command('update')
    .description('Check for updates')
    .action(handleUpdateCommand);

  program
    .command('doctor')
    .description('Diagnose configuration and environment issues')
    .action(handleDoctorCommand);

  // Parse arguments
  program.parse();
}

/**
 * Handle main command (interactive mode or with prompt)
 */
async function handleMainCommand(prompt?: string, options?: any): Promise<void> {
  try {
    // Load base configuration
    let config = loadConfig();

    // Check if onboarding is needed (with API key validation)
    const needsSetup = await needsOnboardingWithValidation(config);
    if (needsSetup) {
      console.log(chalk.yellow('🔧 Initial setup required...'));
      console.log('');

      const onboardingResult = await runOnboarding({
        interactive: true,
        provider: options.provider,
      });

      if (!onboardingResult.success) {
        console.error(chalk.red('Setup failed:'), onboardingResult.error);
        process.exit(1);
      }

      config = onboardingResult.config;
    }

    // Apply command line overrides
    if (options.model) {config.model = options.model;}
    if (options.provider) {config.provider = options.provider;}
    if (options.approval) {config.approvalMode = options.approval as ApprovalPolicy;}
    if (options.temperature !== undefined) {config.temperature = options.temperature;}
    if (options.maxTokens) {config.maxTokens = options.maxTokens;}
    if (options.timeout) {config.timeout = options.timeout;}
    if (options.debug) {config.debug = true;}
    if (options.notifications === false) {config.notifications = false;}
    if (options.history === false) {config.saveHistory = false;}
    if (options.workdir) {config.workingDirectory = resolve(options.workdir);}

    // Validate configuration
    const configValidation = validateConfig(config);
    if (configValidation.errors.length > 0) {
      console.error(chalk.red('Configuration errors:'));
      configValidation.errors.forEach(error => console.error(chalk.red(`  - ${error}`)));
      process.exit(1);
    }

    // Check for updates (non-blocking)
    if (config.notifications) {
      checkForUpdates().catch(() => {
        // Ignore update check failures
      });
    }

    // Log startup information
    if (isLoggingEnabled()) {
      logInfo(`Kritrima AI CLI v${CLI_VERSION} starting`);
      logInfo(`Provider: ${config.provider}, Model: ${config.model}`);
      logInfo(`Working directory: ${config.workingDirectory}`);
    }

    // Handle different modes
    if (options.fullContext || options.singlePass) {
      if (!prompt) {
        console.error(chalk.red('Prompt is required for full-context/single-pass mode'));
        process.exit(1);
      }

      // Import and run single-pass mode
      const { runSinglePass } = await import('./cli-singlepass');
      await runSinglePass({
        originalPrompt: prompt,
        config,
        rootPath: config.workingDirectory || process.cwd(),
      });
    } else {
      // Run main application (interactive mode if no prompt, or with initial prompt)
      await runApp(config, prompt);
    }

  } catch (error) {
    const err = error as Error;
    logError('CLI startup failed', err);
    console.error(chalk.red(`Error: ${err.message}`));
    process.exit(1);
  }
}

/**
 * Handle setup command
 */
async function handleSetupCommand(options: any): Promise<void> {
  try {
    console.log(chalk.blue('🚀 Starting Kritrima AI CLI setup...'));
    console.log('');

    const result = await runOnboarding({
      interactive: true,
      provider: options.provider,
      skipValidation: options.skipValidation,
      force: options.force,
    });

    if (!result.success) {
      console.error(chalk.red('Setup failed:'), result.error);
      process.exit(1);
    }

    console.log(chalk.green('✅ Setup completed successfully!'));
    console.log('');
    console.log(chalk.blue('You can now run:'));
    console.log(chalk.blue('  kritrima-ai          # Start interactive mode'));
    console.log(chalk.blue('  kritrima-ai "prompt" # Run with a prompt'));
    console.log('');

  } catch (error) {
    console.error(chalk.red(`Setup failed: ${(error as Error).message}`));
    process.exit(1);
  }
}

/**
 * Handle config command
 */
async function handleConfigCommand(options: any): Promise<void> {
  try {
    if (options.clear) {
      const { default: inquirer } = await import('inquirer');
      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: 'This will clear all configuration and API keys. Are you sure?',
          default: false,
        },
      ]);

      if (confirm) {
        // Clear environment variables
        const providers = getProviderNames();
        providers.forEach(providerName => {
          const provider = getProvider(providerName);
          if (provider?.envKey) {
            delete process.env[provider.envKey];
          }
        });

        // Reset configuration
        const defaultConfig = {
          model: 'gpt-4',
          provider: 'openai',
          approvalMode: 'suggest' as ApprovalPolicy,
        };
        saveConfig(defaultConfig, options.global);

        console.log(chalk.green('✅ Configuration and API keys cleared'));
        console.log(chalk.blue('Run "kritrima-ai setup" to configure again'));
      } else {
        console.log(chalk.yellow('Operation cancelled'));
      }
      return;
    }

    if (options.wizard) {
      const result = await runConfigurationWizard({
        global: options.global,
      });

      if (!result.success) {
        console.error(chalk.red('Configuration wizard failed:'), result.error);
        process.exit(1);
      }

      console.log(chalk.green('Configuration wizard completed successfully!'));
      return;
    }

    if (options.list) {
      const config = loadConfig();
      console.log(chalk.blue('Current Configuration:'));
      console.log(JSON.stringify(config, null, 2));
      return;
    }

    if (options.reset) {
      const defaultConfig = {
        model: 'gpt-4',
        provider: 'openai',
        approvalMode: 'suggest' as ApprovalPolicy,
      };
      saveConfig(defaultConfig, options.global);
      console.log(chalk.green('Configuration reset to defaults'));
      return;
    }

    if (options.set) {
      const [key, value] = options.set.split('=');
      if (!key || value === undefined) {
        console.error(chalk.red('Invalid format. Use: key=value'));
        process.exit(1);
      }

      const config = loadConfig();
      (config as any)[key] = value;
      saveConfig(config, options.global);
      console.log(chalk.green(`Set ${key} = ${value}`));
      return;
    }

    console.log(chalk.yellow('Available options:'));
    console.log(chalk.yellow('  --list     List current configuration'));
    console.log(chalk.yellow('  --wizard   Run configuration wizard'));
    console.log(chalk.yellow('  --set      Set configuration value (key=value)'));
    console.log(chalk.yellow('  --reset    Reset to default configuration'));
    console.log(chalk.yellow('  --clear    Clear all configuration and API keys'));
  } catch (error) {
    console.error(chalk.red(`Config command failed: ${(error as Error).message}`));
    process.exit(1);
  }
}

/**
 * Handle providers command
 */
async function handleProvidersCommand(options: any): Promise<void> {
  try {
    const providers = getProviderNames();
    
    console.log(chalk.blue('Available Providers:'));
    for (const provider of providers) {
      console.log(chalk.green(`  - ${provider}`));
    }

    if (options.verbose) {
      const { getProvider } = await import('./utils/providers');
      console.log('\nProvider Details:');
      for (const providerName of providers) {
        const provider = getProvider(providerName);
        if (provider) {
          console.log(chalk.cyan(`\n${provider.name}:`));
          console.log(`  Base URL: ${provider.baseURL}`);
          console.log(`  Environment Key: ${provider.envKey}`);
          console.log(`  Supports Images: ${provider.supportsImages ? 'Yes' : 'No'}`);
          console.log(`  Supports Tools: ${provider.supportsTools ? 'Yes' : 'No'}`);
          console.log(`  Max Context: ${provider.maxContextLength} tokens`);
        }
      }
    }
  } catch (error) {
    console.error(chalk.red(`Providers command failed: ${(error as Error).message}`));
    process.exit(1);
  }
}

/**
 * Handle models command
 */
async function handleModelsCommand(provider?: string): Promise<void> {
  try {
    const config = loadConfig();
    const targetProvider = provider || config.provider;

    if (!hasProvider(targetProvider)) {
      console.error(chalk.red(`Unknown provider: ${targetProvider}`));
      process.exit(1);
    }

    console.log(chalk.blue(`Fetching models for ${targetProvider}...`));
    
    const { fetchModels } = await import('./utils/model-utils');
    const models = await fetchModels(targetProvider);

    if (models.length === 0) {
      console.log(chalk.yellow('No models found'));
      return;
    }

    console.log(chalk.green(`Available models for ${targetProvider}:`));
    models.forEach(model => console.log(`  - ${model}`));
  } catch (error) {
    console.error(chalk.red(`Models command failed: ${(error as Error).message}`));
    process.exit(1);
  }
}

/**
 * Handle update command
 */
async function handleUpdateCommand(): Promise<void> {
  try {
    const { checkForUpdates } = await import('./utils/check-updates');
    await checkForUpdates();
  } catch (error) {
    console.error(chalk.red(`Update check failed: ${(error as Error).message}`));
    process.exit(1);
  }
}

/**
 * Handle doctor command
 */
async function handleDoctorCommand(): Promise<void> {
  try {
    console.log(chalk.blue('Kritrima AI CLI Diagnostics'));
    console.log('='.repeat(40));

    // Version information
    console.log(chalk.cyan('\nVersion Information:'));
    console.log(formatVersionInfo());

    // Configuration
    console.log(chalk.cyan('\nConfiguration:'));
    const config = loadConfig();
    const configValidation = validateConfig(config);

    if (configValidation.errors.length === 0) {
      console.log(chalk.green('✓ Configuration is valid'));
    } else {
      console.log(chalk.red('✗ Configuration has errors:'));
      configValidation.errors.forEach(error => console.log(chalk.red(`  - ${error}`)));
    }

    // API Keys
    console.log(chalk.cyan('\nAPI Keys:'));
    const { getApiKey } = await import('./utils/config');
    const providers = getProviderNames();
    
    for (const provider of providers) {
      const apiKey = getApiKey(provider);
      if (apiKey) {
        console.log(chalk.green(`✓ ${provider}: API key found`));
      } else {
        console.log(chalk.yellow(`- ${provider}: No API key`));
      }
    }

    // Working directory
    console.log(chalk.cyan('\nWorking Directory:'));
    const workdir = config.workingDirectory || process.cwd();
    if (existsSync(workdir)) {
      console.log(chalk.green(`✓ ${workdir} exists`));
    } else {
      console.log(chalk.red(`✗ ${workdir} does not exist`));
    }

    // Git repository
    console.log(chalk.cyan('\nGit Repository:'));
    const { checkInGit } = await import('./utils/check-in-git');
    if (checkInGit(workdir)) {
      console.log(chalk.green('✓ Inside Git repository'));
    } else {
      console.log(chalk.yellow('- Not in Git repository'));
    }

    console.log(chalk.blue('\nDiagnostics complete'));
  } catch (error) {
    console.error(chalk.red(`Doctor command failed: ${(error as Error).message}`));
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  logError('Uncaught exception', error);
  console.error(chalk.red('Fatal error:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  logError('Unhandled rejection', reason as Error);
  console.error(chalk.red('Unhandled promise rejection:'), reason);
  process.exit(1);
});

// Run the CLI
main().catch((error) => {
  logError('Main function failed', error);
  console.error(chalk.red('Failed to start CLI:'), error.message);
  process.exit(1);
});
